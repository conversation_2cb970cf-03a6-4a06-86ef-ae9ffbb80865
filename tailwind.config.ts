import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: '#9500FF',
          50: '#F5E6FF',
          100: '#E8CCFF',
          200: '#D199FF',
          300: '#BA66FF',
          400: '#A333FF',
          500: '#9500FF',
          600: '#7600CC',
          700: '#5A0099',
          800: '#3D0066',
          900: '#1F0033'
        },
        accent: {
          DEFAULT: '#FF00FF',
          500: '#FF00FF',
          600: '#E600E6',
          700: '#CC00CC'
        },
        neon: {
          purple: '#9500FF',
          pink: '#FF00FF',
          cyan: '#00FFFF'
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        display: ['Inter', 'system-ui', 'sans-serif'],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.6s ease-out',
        'gradient': 'gradient 6s ease infinite',
        'glow': 'glow 3s ease-in-out infinite',
        'float': 'float 6s ease-in-out infinite',
        'pulse-ring': 'pulse-ring 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        gradient: {
          '0%, 100%': {
            'background-size': '200% 200%',
            'background-position': 'left center'
          },
          '50%': {
            'background-size': '200% 200%',
            'background-position': 'right center'
          },
        },
      },
      boxShadow: {
        'neon': '0 0 20px rgba(149, 0, 255, 0.5)',
        'neon-lg': '0 0 40px rgba(149, 0, 255, 0.7)',
      },
    },
  },
  plugins: [],
};

export default config; 