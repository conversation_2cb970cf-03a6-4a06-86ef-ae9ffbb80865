# OF AutoFollower

<div align="center">
  <img src="public/favicon.png" alt="OF AutoFollower Logo" width="120" height="120">
  
  **The #1 Chrome Extension for OnlyFans Creators**
  
  Maximize your OnlyFans revenue by recovering expired fans and attracting new subscribers with powerful automation tools.

  [![Chrome Web Store](https://img.shields.io/badge/Chrome-Extension-4285F4?style=for-the-badge&logo=google-chrome&logoColor=white)](https://chrome.google.com/webstore/detail/follow-bot-for-onlyfans-e/plklbjmdembgfdaafefofjllkgfpjepf)
  [![Website](https://img.shields.io/badge/Website-ofautofollower.com-8B5CF6?style=for-the-badge&logo=web&logoColor=white)](https://ofautofollower.com)
  [![License](https://img.shields.io/badge/License-MIT-green.svg?style=for-the-badge)](LICENSE)
</div>

## 🚀 About OF AutoFollower

OF AutoFollower is a powerful browser extension and web platform designed specifically for OnlyFans creators to maximize their revenue through intelligent automation. Our tools help creators recover lost earnings from expired fans, attract new subscribers, and optimize their content strategy.

### 💰 Revenue Impact
- **$2,000,000+** in revenue recovered for creators
- **1,000+** active creators trust our platform
- **40%** average increase in monthly earnings

## ✨ Key Features

### 🔄 **Expired Fan Recovery**
- Automatically follow expired fans to maintain messaging capabilities
- Recover lost revenue through targeted outreach campaigns
- Smart filtering to identify high-value expired subscribers

### 🎯 **New Fan Acquisition**
- Follow brand new OnlyFans users before competitors
- AI-sourced database of fresh prospects
- Ethical targeting within platform guidelines

### 🥇 **Priority Mass Messaging**
- Send messages that appear first in fans' inboxes
- Personalized messaging with dynamic variables
- Higher open rates and engagement

### 📊 **Advanced Analytics**
- Identify high-spending fans vs. time-wasters
- Track spending patterns and rebill status
- Optimize your content strategy with data insights

### 🛡️ **Safety & Compliance**
- Human-like behavior patterns to stay within OnlyFans limits
- Account protection with built-in safety measures
- Regular updates to maintain platform compliance

## 🛠️ Tech Stack

| Technology | Purpose |
|------------|---------|
| **[Next.js 15](https://nextjs.org/)** | React framework with App Router |
| **[TypeScript](https://www.typescriptlang.org/)** | Type-safe development |
| **[Tailwind CSS](https://tailwindcss.com/)** | Utility-first CSS framework |
| **[Framer Motion](https://www.framer.com/motion/)** | Advanced animations |
| **[AOS](https://michalsnik.github.io/aos/)** | Scroll animations |
| **[Lucide React](https://lucide.dev/)** | Beautiful icons |
| **[ESLint](https://eslint.org/)** | Code linting and formatting |

## 📁 Project Structure

```
ofautofollower.com/
├── public/                 # Static assets
│   ├── images/            # Image assets
│   └── favicon.png        # Site favicon
├── src/
│   ├── app/               # Next.js App Router pages
│   │   ├── affiliate/     # Affiliate program page
│   │   ├── bot/           # Bot-related pages
│   │   ├── privacy-policy/# Privacy policy page
│   │   ├── globals.css    # Global styles
│   │   ├── layout.tsx     # Root layout
│   │   └── page.tsx       # Homepage
│   ├── components/        # Reusable React components
│   │   ├── sections/      # Page sections
│   │   └── ui/            # UI components
│   └── lib/               # Utility functions
├── logos/                 # Brand assets
├── package.json           # Dependencies and scripts
├── tailwind.config.ts     # Tailwind configuration
└── tsconfig.json          # TypeScript configuration
```

## 🚀 Quick Start

### Prerequisites
- **Node.js** 18.0 or later
- **npm** or **yarn** package manager

### Installation


1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Start development server**
   ```bash
   npm run dev
   ```

3. **Open your browser**
   
   Navigate to [http://localhost:3000](http://localhost:3000) to see the application.

## 📜 Available Scripts

| Command | Description |
|---------|-------------|
| `npm run dev` | Start development server with hot reload |
| `npm run build` | Build optimized production bundle |
| `npm run start` | Start production server |
| `npm run lint` | Run ESLint for code quality checks |
| `npm run lint:fix` | Fix auto-fixable ESLint issues |

## 🌐 Pages & Routes

- **`/`** - Homepage with features and hero section
- **`/affiliate`** - Affiliate program information and signup
- **`/bot/pricing`** - Pricing plans and subscription options
- **`/privacy-policy`** - Comprehensive privacy policy

## 🎨 Design System

### Color Palette
- **Primary**: Purple gradients (`#8B5CF6` to `#EC4899`)
- **Background**: Dark theme with gradient overlays
- **Accent**: Cyan (`#06B6D4`) and emerald (`#10B981`)

### Typography
- **Font**: Inter (Google Fonts)
- **Headings**: Bold weights with gradient text effects
- **Body**: Regular weight with optimized line height

### Components
- **Glass morphism** effects for cards and modals
- **Hover animations** with smooth transitions
- **Responsive design** for all screen sizes

### Development Guidelines
- Follow TypeScript best practices
- Use Tailwind CSS for styling
- Ensure responsive design
- Add proper error handling
- Write meaningful commit messages

## 🚀 Deployment

### Vercel
1. Connect your GitHub repository to Vercel
2. Configure environment variables
3. Deploy with automatic CI/CD

### Manual Deployment
```bash
npm run build
npm run start
```