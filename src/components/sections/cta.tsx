"use client";

import { useState, useEffect } from 'react';
import { ArrowRight } from 'lucide-react';

export function CTA() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);
  return (
    <section className="py-24 section-bg">
      <div className="max-w-4xl mx-auto text-center px-6 lg:px-8">
        {/* Background decoration */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-purple-600/20 to-pink-600/20 rounded-full blur-3xl"></div>
        </div>

        <div className="relative" data-aos="fade-up">
          {/* Badge */}
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full glass-effect text-sm font-medium text-gray-300 mb-8">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            Limited time offer
          </div>

          {/* Heading */}
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6 text-balance">
            Ready to double your
            <br />
            <span className="gradient-text">earnings?</span>
          </h2>

          {/* Subheading */}
          <p className="text-xl text-gray-400 mb-12 max-w-2xl mx-auto text-balance">
            Join thousands of creators who&apos;ve already recovered their lost revenue and transformed their OnlyFans business.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            {mounted && (
              <a
                href="https://chrome.google.com/webstore/detail/follow-bot-for-onlyfans-e/plklbjmdembgfdaafefofjllkgfpjepf"
                target="_blank"
                className="group bg-white/95 hover:bg-white text-gray-900 font-semibold px-8 py-4 rounded-xl transition-all duration-200 inline-flex items-center gap-3 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 backdrop-blur-sm"
              >
                <svg xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" viewBox="0 0 48 48" height="24" width="24" className="w-6 h-6"><defs><linearGradient id="ctaChromeIconA" x1="3.2173" y1="15" x2="44.7812" y2="15" gradientUnits="userSpaceOnUse"><stop offset="0" stopColor="#d93025"/><stop offset="1" stopColor="#ea4335"/></linearGradient><linearGradient id="ctaChromeIconB" x1="20.7219" y1="47.6791" x2="41.5039" y2="11.6837" gradientUnits="userSpaceOnUse"><stop offset="0" stopColor="#fcc934"/><stop offset="1" stopColor="#fbbc04"/></linearGradient><linearGradient id="ctaChromeIconC" x1="26.5981" y1="46.5015" x2="5.8161" y2="10.506" gradientUnits="userSpaceOnUse"><stop offset="0" stopColor="#1e8e3e"/><stop offset="1" stopColor="#34a853"/></linearGradient></defs><circle cx="24" cy="23.9947" r="12" style={{fill:'#fff'}}/><path d="M3.2154,36A24,24,0,1,0,12,3.2154,24,24,0,0,0,3.2154,36ZM34.3923,18A12,12,0,1,1,18,13.6077,12,12,0,0,1,34.3923,18Z" style={{fill:'none'}}/><path d="M24,12H44.7812a23.9939,23.9939,0,0,0-41.5639.0029L13.6079,30l.0093-.0024A11.9852,11.9852,0,0,1,24,12Z" style={{fill:'url(#ctaChromeIconA)'}}/><circle cx="24" cy="24" r="9.5" style={{fill:'#1a73e8'}}/><path d="M34.3913,30.0029,24.0007,48A23.994,23.994,0,0,0,44.78,12.0031H23.9989l-.0025.0093A11.985,11.985,0,0,1,34.3913,30.0029Z" style={{fill:'url(#ctaChromeIconB)'}}/><path d="M13.6086,30.0031,3.218,12.006A23.994,23.994,0,0,0,24.0025,48L34.3931,30.0029l-.0067-.0068a11.9852,11.9852,0,0,1-20.7778.007Z" style={{fill:'url(#ctaChromeIconC)'}}/></svg>
                Get Started Now
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </a>
            )}
            
            <div className="text-center sm:text-left">
              <div className="text-sm text-gray-400">
                ✨ 50% off for new users
              </div>
              <div className="text-sm text-gray-400">
                🛡️ 100% safe & secure
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
} 