"use client";

import Image from "next/image";
import Link from "next/link";
import { useState, useEffect } from "react";

export function Navigation() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    // Check initial scroll position after mount
    setIsScrolled(window.scrollY > 50);

    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <nav
      className={`fixed top-0 w-full z-50 transition-all duration-300 ${
        mounted && isScrolled
          ? 'bg-black/80 backdrop-blur-xl border-b border-purple-500/20 shadow-lg shadow-purple-500/10'
          : 'bg-transparent border-b border-transparent'
      }`}
    >
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center space-x-3">
            <Link href="/" className="flex items-center gap-3 hover:opacity-80 transition-opacity" aria-label="OF AutoFollower - Home">
              <Image 
                src="/OFAFlogo.png" 
                alt="OF AutoFollower Logo" 
                priority 
                width={45} 
                height={45}
                className="w-11 h-11"
              />
              <span className="text-xl font-bold bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent">
                ofautofollower
              </span>
            </Link>
          </div>
          
          <div className="hidden md:flex items-center space-x-8">
            <a href="#features" className="text-gray-300 hover:text-white font-medium transition-colors text-sm relative group focus:outline-none px-2 py-1">
              Features
              <span className="absolute bottom-0 left-2 w-0 h-0.5 bg-gradient-to-r from-purple-500 to-pink-500 transition-all duration-300 group-hover:w-full"></span>
            </a>
            <a href="#faq" className="text-gray-300 hover:text-white font-medium transition-colors text-sm relative group focus:outline-none px-2 py-1">
              FAQ
              <span className="absolute bottom-0 left-2 w-0 h-0.5 bg-gradient-to-r from-purple-500 to-pink-500 transition-all duration-300 group-hover:w-full"></span>
            </a>
            <a href="/bot/pricing" className="text-gray-300 hover:text-white font-medium transition-colors text-sm relative group focus:outline-none px-2 py-1">
              Pricing
              <span className="absolute bottom-0 left-2 w-0 h-0.5 bg-gradient-to-r from-purple-500 to-pink-500 transition-all duration-300 group-hover:w-full"></span>
            </a>
            <a href="/affiliate" className="text-gray-300 hover:text-white font-medium transition-colors text-sm relative group focus:outline-none px-2 py-1">
              Affiliate
              <span className="absolute bottom-0 left-2 w-0 h-0.5 bg-gradient-to-r from-purple-500 to-pink-500 transition-all duration-300 group-hover:w-full"></span>
            </a>
            <a 
              href="https://chrome.google.com/webstore/detail/follow-bot-for-onlyfans-e/plklbjmdembgfdaafefofjllkgfpjepf" 
              target="_blank"
              rel="noopener noreferrer"
              className="premium-button text-white font-semibold px-6 py-2.5 rounded-full text-sm flex items-center gap-2 shadow-neon focus:outline-none"
              aria-label="Download Chrome Extension"
            >
              <svg xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" viewBox="0 0 48 48" height="20" width="20" aria-hidden="true">
                <defs>
                  <linearGradient id="navChromeIconA" x1="3.2173" y1="15" x2="44.7812" y2="15" gradientUnits="userSpaceOnUse">
                    <stop offset="0" stopColor="#d93025"/>
                    <stop offset="1" stopColor="#ea4335"/>
                  </linearGradient>
                  <linearGradient id="navChromeIconB" x1="20.7219" y1="47.6791" x2="41.5039" y2="11.6837" gradientUnits="userSpaceOnUse">
                    <stop offset="0" stopColor="#fcc934"/>
                    <stop offset="1" stopColor="#fbbc04"/>
                  </linearGradient>
                  <linearGradient id="navChromeIconC" x1="26.5981" y1="46.5015" x2="5.8161" y2="10.506" gradientUnits="userSpaceOnUse">
                    <stop offset="0" stopColor="#1e8e3e"/>
                    <stop offset="1" stopColor="#34a853"/>
                  </linearGradient>
                </defs>
                <circle cx="24" cy="23.9947" r="12" style={{fill:'#fff'}}/>
                <path d="M3.2154,36A24,24,0,1,0,12,3.2154,24,24,0,0,0,3.2154,36ZM34.3923,18A12,12,0,1,1,18,13.6077,12,12,0,0,1,34.3923,18Z" style={{fill:'none'}}/>
                <path d="M24,12H44.7812a23.9939,23.9939,0,0,0-41.5639.0029L13.6079,30l.0093-.0024A11.9852,11.9852,0,0,1,24,12Z" style={{fill:'url(#navChromeIconA)'}}/>
                <circle cx="24" cy="24" r="9.5" style={{fill:'#1a73e8'}}/>
                <path d="M34.3913,30.0029,24.0007,48A23.994,23.994,0,0,0,44.78,12.0031H23.9989l-.0025.0093A11.985,11.985,0,0,1,34.3913,30.0029Z" style={{fill:'url(#navChromeIconB)'}}/>
                <path d="M13.6086,30.0031,3.218,12.006A23.994,23.994,0,0,0,24.0025,48L34.3931,30.0029l-.0067-.0068a11.9852,11.9852,0,0,1-20.7778.007Z" style={{fill:'url(#navChromeIconC)'}}/>
              </svg>
              Get Extension
            </a>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button 
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="p-2 rounded-md text-gray-300 hover:text-white hover:bg-purple-500/20 transition-colors focus:outline-none"
              aria-label={isMobileMenuOpen ? "Close mobile menu" : "Open mobile menu"}
              aria-expanded={isMobileMenuOpen}
              aria-controls="mobile-menu"
            >
              {isMobileMenuOpen ? (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              ) : (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu Dropdown */}
      {mounted && isMobileMenuOpen && (
        <div id="mobile-menu" className="md:hidden bg-black/90 backdrop-blur-lg border-t border-purple-500/20">
          <div className="px-4 pt-4 pb-6 space-y-1 flex flex-col">
            <a 
              href="#features" 
              onClick={() => setIsMobileMenuOpen(false)}
              className="text-gray-300 hover:text-white hover:bg-purple-500/10 block px-4 py-3 rounded-lg text-base font-medium transition-colors focus:outline-none"
            >
              Features
            </a>
            <a 
              href="#faq" 
              onClick={() => setIsMobileMenuOpen(false)}
              className="text-gray-300 hover:text-white hover:bg-purple-500/10 block px-4 py-3 rounded-lg text-base font-medium transition-colors focus:outline-none"
            >
              FAQ
            </a>
            <a 
              href="/bot/pricing" 
              onClick={() => setIsMobileMenuOpen(false)}
              className="text-gray-300 hover:text-white hover:bg-purple-500/10 block px-4 py-3 rounded-lg text-base font-medium transition-colors focus:outline-none"
            >
              Pricing
            </a>
            <a 
              href="/affiliate" 
              onClick={() => setIsMobileMenuOpen(false)}
              className="text-gray-300 hover:text-white hover:bg-purple-500/10 block px-4 py-3 rounded-lg text-base font-medium transition-colors focus:outline-none"
            >
              Affiliate
            </a>
            <a 
              href="https://chrome.google.com/webstore/detail/follow-bot-for-onlyfans-e/plklbjmdembgfdaafefofjllkgfpjepf" 
              target="_blank"
              rel="noopener noreferrer"
              onClick={() => setIsMobileMenuOpen(false)}
              className="mt-4 premium-button text-white font-semibold px-6 py-3 rounded-full text-base w-full flex items-center justify-center gap-2 focus:outline-none"
              aria-label="Download Chrome Extension"
            >
              <svg xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" viewBox="0 0 48 48" height="20" width="20" aria-hidden="true">
                <defs>
                  <linearGradient id="navMobileChromeIconA" x1="3.2173" y1="15" x2="44.7812" y2="15" gradientUnits="userSpaceOnUse">
                    <stop offset="0" stopColor="#d93025"/>
                    <stop offset="1" stopColor="#ea4335"/>
                  </linearGradient>
                  <linearGradient id="navMobileChromeIconB" x1="20.7219" y1="47.6791" x2="41.5039" y2="11.6837" gradientUnits="userSpaceOnUse">
                    <stop offset="0" stopColor="#fcc934"/>
                    <stop offset="1" stopColor="#fbbc04"/>
                  </linearGradient>
                  <linearGradient id="navMobileChromeIconC" x1="26.5981" y1="46.5015" x2="5.8161" y2="10.506" gradientUnits="userSpaceOnUse">
                    <stop offset="0" stopColor="#1e8e3e"/>
                    <stop offset="1" stopColor="#34a853"/>
                  </linearGradient>
                </defs>
                <circle cx="24" cy="23.9947" r="12" style={{fill:'#fff'}}/>
                <path d="M3.2154,36A24,24,0,1,0,12,3.2154,24,24,0,0,0,3.2154,36ZM34.3923,18A12,12,0,1,1,18,13.6077,12,12,0,0,1,34.3923,18Z" style={{fill:'none'}}/>
                <path d="M24,12H44.7812a23.9939,23.9939,0,0,0-41.5639.0029L13.6079,30l.0093-.0024A11.9852,11.9852,0,0,1,24,12Z" style={{fill:'url(#navMobileChromeIconA)'}}/>
                <circle cx="24" cy="24" r="9.5" style={{fill:'#1a73e8'}}/>
                <path d="M34.3913,30.0029,24.0007,48A23.994,23.994,0,0,0,44.78,12.0031H23.9989l-.0025.0093A11.985,11.985,0,0,1,34.3913,30.0029Z" style={{fill:'url(#navMobileChromeIconB)'}}/>
                <path d="M13.6086,30.0031,3.218,12.006A23.994,23.994,0,0,0,24.0025,48L34.3931,30.0029l-.0067-.0068a11.9852,11.9852,0,0,1-20.7778.007Z" style={{fill:'url(#navMobileChromeIconC)'}}/>
              </svg>
              Get Extension
            </a>
          </div>
        </div>
      )}
    </nav>
  );
} 