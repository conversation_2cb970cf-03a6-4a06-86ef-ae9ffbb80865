"use client";

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
// NOTE: we load the heavy AOS bundle lazily so it doesn't block the main thread.
import 'aos/dist/aos.css';

export function AOSInit() {
  const pathname = usePathname();

  useEffect(() => {
    const initAOS = async () => {
      // Dynamically import AOS only when needed
      const AOS = (await import('aos')).default;
      
      // Initialize AOS
      AOS.init({
        duration: 400,
        once: false, // Allow animations to re-trigger on navigation
        offset: 50,
        easing: 'ease-out-cubic',
        delay: 0,
        // Performance optimizations
        disable: window.innerWidth < 768, // Disable on mobile for better performance
        disableMutationObserver: false, // Enable to detect new elements
        debounceDelay: 50,
        throttleDelay: 100,
        // Reduce animation distance for better performance
        startEvent: 'DOMContentLoaded',
      });

      // Refresh AOS to detect any new elements
      setTimeout(() => {
        AOS.refresh();
      }, 100);
    };

    // Use requestIdleCallback for better performance
    if ('requestIdleCallback' in window) {
      window.requestIdleCallback?.(initAOS, { timeout: 2000 });
    } else {
      // Fallback for browsers without requestIdleCallback
      setTimeout(initAOS, 100);
    }
  }, [pathname]); // Re-run when pathname changes

  return null; // This component doesn't render anything
} 