"use client";

import { Check, Chrome, Star, Users, DollarSign } from 'lucide-react';
import { Navigation } from "@/components/sections/navigation";
import { Footer } from "@/components/sections/footer";
import { useState, useEffect } from 'react';

export default function PricingPage() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const keyFeatures = [
    "Follow expired fans with 1 click",
    "Access fresh OnlyFans signups daily",
    "Send priority mass messages",
    "Cross-promote between accounts",
    "Identify high-spending fans",
    "100% safe & secure"
  ];

  return (
    <main className="min-h-screen">
      <Navigation />
      
      {/* Hero Section */}
      <section className="hero-gradient min-h-screen flex items-center pt-20">
        <div className="max-w-4xl mx-auto px-6 lg:px-8 text-center">
          
          {/* Header */}
          <div className="mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full glass-effect text-sm font-medium text-gray-300 mb-8">
              <DollarSign className="w-4 h-4" />
              Simple Pricing
            </div>
            
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Start earning more
              <br />
              <span className="gradient-text">today</span>
            </h1>
            
            <p className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto">
              Everything you need to maximize your OnlyFans revenue in one simple plan.
            </p>
          </div>

          {/* Pricing Card */}
          <div className="card-bg rounded-3xl p-8 md:p-12 max-w-2xl mx-auto mb-16">
            
            {/* Price */}
            <div className="text-center mb-8">
                             <div className="flex items-center justify-center gap-3 mb-4">
                 <span className="text-2xl text-gray-500 line-through">$149</span>
                 <div className="bg-red-500/20 text-red-400 px-3 py-1 rounded-full text-sm font-semibold">
                   33% OFF
                 </div>
               </div>
               
               <div className="mb-6">
                 <span className="text-6xl md:text-7xl font-bold bg-gradient-to-br from-gray-100 via-white to-gray-200 bg-clip-text text-transparent">$99</span>
                 <span className="text-2xl text-gray-400 ml-2">/month</span>
               </div>
              
              <p className="text-gray-400">
                Cancel anytime • No setup fees • Instant access
              </p>
            </div>

            {/* Features */}
            <div className="mb-8">
              <div className="grid gap-4">
                {keyFeatures.map((feature, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <Check className="w-3 h-3 text-white" />
                    </div>
                    <span className="text-white font-medium">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

                        {/* CTA */}
            <a 
              href="https://chrome.google.com/webstore/detail/follow-bot-for-onlyfans-e/plklbjmdembgfdaafefofjllkgfpjepf" 
              target="_blank"
              className={`${mounted ? 'premium-button' : 'bg-purple-600'} text-white font-bold px-8 py-4 rounded-xl inline-flex items-center gap-3 shadow-lg w-full justify-center text-lg mb-6`}
            >
              <Chrome className="w-6 h-6" />
              Get Started Now
            </a>

            {/* Trust indicators */}
            <div className="flex items-center justify-center gap-6 pt-6 border-t border-white/10 text-sm text-gray-400">
              <div className="flex items-center gap-1">
                <div className="flex text-yellow-400">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-current" />
                  ))}
                </div>
                <span className="ml-1">4.7/5 rating</span>
              </div>
              <span>•</span>
              <div className="flex items-center gap-1">
                <Users className="w-4 h-4" />
                <span>1,000+ creators</span>
              </div>
            </div>
          </div>

          {/* Social proof stats */}
          <div className="grid grid-cols-3 gap-8 max-w-2xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-400 mb-2">$2M+</div>
              <div className="text-gray-400 text-sm">Revenue recovered</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-400 mb-2">50K+</div>
              <div className="text-gray-400 text-sm">Fans recovered</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-400 mb-2">99%</div>
              <div className="text-gray-400 text-sm">Uptime</div>
            </div>
          </div>
        </div>
      </section>

      {/* Simple FAQ */}
      <section className="py-20 section-bg">
        <div className="max-w-3xl mx-auto px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-white text-center mb-12">
            Common Questions
          </h2>
          
          <div className="space-y-6">
            {[
              {
                q: "Is this safe for my OnlyFans account?",
                a: "Yes, we use human-like behavior patterns and stay within OnlyFans limits. 1,000+ creators use our tool safely."
              },
              {
                q: "How quickly will I see results?",
                a: "Most creators see new followers within 24 hours and increased revenue within the first week."
              },
              {
                q: "Can I cancel anytime?",
                a: "Absolutely. Cancel your subscription anytime with no cancellation fees or penalties."
              },
              {
                q: "Do you offer support?",
                a: "Yes, we provide 24/7 email support to help you maximize your results."
              }
            ].map((faq, index) => (
              <div key={index} className="card-bg p-6 rounded-xl">
                <h3 className="text-lg font-semibold text-white mb-3">{faq.q}</h3>
                <p className="text-gray-400">{faq.a}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
      
      <Footer />
    </main>
  );
} 