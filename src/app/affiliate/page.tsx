"use client";

import { useState, useEffect } from 'react';
import { CheckCircle, DollarSign, TrendingUp, ArrowR<PERSON>, Copy, Check } from 'lucide-react';
import { Navigation } from '@/components/sections/navigation';

export default function AffiliatePage() {
  const [copied, setCopied] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleCopyCode = () => {
    if (typeof window !== 'undefined' && navigator.clipboard) {
      navigator.clipboard.writeText('EXAMPLE20');
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  return (
    <>
      <Navigation />
      <main className="min-h-screen hero-gradient pt-20">
        <div className="max-w-4xl mx-auto px-6 lg:px-8">
          {/* Compact Hero */}
          <div className="text-center py-16">
            <div className="inline-flex items-center gap-2 px-3 py-1.5 rounded-full glass-effect text-xs font-medium text-gray-300 mb-6 animate-glow">
              <DollarSign className="w-3.5 h-3.5" />
              Affiliate Program
            </div>

            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 tracking-tight">
              Earn <span className="gradient-text">$20/mo for life</span>
              <br />
              for every referral
            </h1>
            <p className="text-lg text-gray-300 mb-8 max-w-2xl mx-auto">
              Start earning recurring income today.
            </p>

                        {/* Compact Benefits Grid */}
            <div className="flex justify-center gap-8 mb-8">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                  <DollarSign className="w-4 h-4 text-white" />
                </div>
                <div className="text-left">
                  <p className="text-sm font-medium text-white">$20/month</p>
                  <p className="text-xs text-gray-400">Per referral, forever</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-4 h-4 text-white" />
                </div>
                <div className="text-left">
                  <p className="text-sm font-medium text-white">No limits</p>
                  <p className="text-xs text-gray-400">Unlimited earnings</p>
                </div>
              </div>
            </div>

            {/* Compact Benefits List */}
            <div className="p-6 rounded-xl mb-8 max-w-2xl mx-auto">
              <div className="space-y-3">
                <div className="flex items-center gap-3 justify-center">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <p className="text-white">You get $20 per month for every referral, forever</p>
                </div>
                <div className="flex items-center gap-3 justify-center">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <p className="text-white">No earning limits — the more you refer, the more you earn</p>
                </div>
              </div>
            </div>

            {/* Sample Code */}
            <div className="card-bg p-4 rounded-xl mb-6">
              <p className="text-gray-400 text-sm mb-2">Your referral code will look like:</p>
              <div className="flex items-center justify-center gap-2">
                <code className="bg-gray-800/50 px-3 py-2 rounded-lg text-white font-mono">
                  EXAMPLE20
                </code>
                <button
                  onClick={handleCopyCode}
                  className="p-2 hover:bg-gray-700/50 rounded-lg transition-colors"
                  title="Copy code"
                >
                  {copied ? (
                    <Check className="w-4 h-4 text-green-400" />
                  ) : (
                    <Copy className="w-4 h-4 text-gray-400" />
                  )}
                </button>
              </div>
            </div>

                        {/* Get Started Button */}
            <div className="space-y-4">
              <button 
                className={`${mounted ? 'premium-button' : 'bg-purple-600'} text-white font-semibold px-8 py-4 rounded-xl inline-flex items-center gap-3 shadow-lg`}
              >
                Get Started
                <ArrowRight className="w-5 h-5" />
              </button>

              <p className="text-gray-500 text-sm">
                Or contact us at{' '}
                <a href="mailto:<EMAIL>" className="text-purple-400 hover:text-purple-300 transition-colors">
                  <EMAIL>
                </a>
              </p>
            </div>
          </div>
        </div>
      </main>
    </>
  );
} 